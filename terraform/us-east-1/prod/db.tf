resource "random_password" "db_pass" {
  length = 10
}

resource "aws_db_instance" "thankview_db" {
  identifier                   = "thankview-prod"
  db_name                      = "thankview"
  engine                       = "mysql"
  engine_version               = "5.7.38"
  instance_class               = "db.m5.2xlarge"
  allocated_storage            = 200
  max_allocated_storage        = 400
  storage_type                 = "gp3"
  storage_encrypted            = true
  username                     = "thankview"
  password                     = random_password.db_pass.result
  monitoring_interval          = 30
  performance_insights_enabled = true
  deletion_protection          = true

  // Creates a multi-availability zone fallover.
  multi_az            = true
  publicly_accessible = true

  // Backup related
  backup_retention_period = 30
  backup_window           = "08:00-08:30"

  vpc_security_group_ids = [
    aws_security_group.webserver_mysql_access.id,
    aws_security_group.webserver_mysql_access_readonly.id,
  ]

  enabled_cloudwatch_logs_exports = ["error", "general", "slowquery"]

  lifecycle {
    prevent_destroy = true
    ignore_changes = [
      password
    ]
  }
}
