data "aws_ami" "ubuntu_20" {
  most_recent = true

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-*-20.04-amd64-server*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  owners = ["099720109477"] # Canonical's ID number
}

module "styledocs_host" {
  source                 = "terraform-aws-modules/ec2-instance/aws"
  version                = "~> 5.6"
  ami                    = data.aws_ami.ubuntu_20.id
  ignore_ami_changes     = true
  name                   = "prod-styledocs-host"
  instance_type          = "t3a.large"
  iam_instance_profile   = "thankview-ssm-ec2-instance-role"
  key_name               = "thankview-prod-ec2"
  subnet_id              = local.instance_subnet_id
  vpc_security_group_ids = [aws_security_group.webserver_load_balancer.id, aws_security_group.thankview-ssh-access.id]
  enable_volume_tags     = false
  root_block_device = [
    {
      encrypted   = true
      volume_type = "gp3"
      throughput  = 125
      volume_size = 32
      tags = {
        Name = "prod-styledocs-host"
      }
    },
  ]
  user_data_replace_on_change = false
  tags = {
    VantaProd        = "true"
    VantaDescription = "Styleguide and api documentation"
    VantaOwner       = "<EMAIL>"
  }
}

module "api_host" {
  source                 = "terraform-aws-modules/ec2-instance/aws"
  version                = "~> 5.6"
  ami                    = data.aws_ami.ubuntu_20.id
  ignore_ami_changes     = true
  name                   = "prod-api-host"
  instance_type          = "t3a.large"
  iam_instance_profile   = "thankview-ssm-ec2-instance-role"
  key_name               = "thankview-prod-ec2"
  subnet_id              = local.instance_subnet_id
  vpc_security_group_ids = [aws_security_group.webserver_load_balancer.id, aws_security_group.thankview-ssh-access.id]
  enable_volume_tags     = true
  root_block_device = [
    {
      encrypted   = true
      volume_type = "gp3"
      throughput  = 125
      volume_size = 32
    },
  ]
  user_data_replace_on_change = false
  tags = {
    VantaProd        = "true"
    VantaDescription = "Public API gateway"
    VantaOwner       = "<EMAIL>"
    APP_ROLE         = "api"
    Environment      = "prod"
    Name             = "prod-api-host"
    Service          = "infrastructure"
    Team             = "devops"
  }
}

module "builder_host" {
  source                 = "terraform-aws-modules/ec2-instance/aws"
  version                = "~> 5.6"
  ami                    = data.aws_ami.ubuntu_20.id
  ignore_ami_changes     = true
  name                   = "prod-builder-host"
  instance_type          = "t3a.large"
  iam_instance_profile   = "thankview-ssm-ec2-instance-role"
  key_name               = "thankview-prod-ec2"
  subnet_id              = local.instance_subnet_id
  vpc_security_group_ids = [aws_security_group.webserver_load_balancer.id, aws_security_group.thankview-ssh-access.id]
  enable_volume_tags     = true
  root_block_device = [
    {
      encrypted   = true
      volume_type = "gp3"
      throughput  = 125
      volume_size = 32
    },
  ]
  user_data_replace_on_change = false
  tags = {
    VantaProd        = "true"
    VantaDescription = "ThankView Asset Builder"
    VantaOwner       = "<EMAIL>"
  }
}

module "jenkins_host" {
  source                 = "terraform-aws-modules/ec2-instance/aws"
  version                = "~> 5.6"
  ami                    = data.aws_ami.ubuntu_20.id
  ignore_ami_changes     = true
  name                   = "prod-jenkins-host"
  instance_type          = "t3a.large"
  iam_instance_profile   = "thankview-ssm-ec2-instance-role"
  key_name               = "thankview-prod-ec2"
  subnet_id              = local.instance_subnet_id
  vpc_security_group_ids = [aws_security_group.webserver_load_balancer.id, aws_security_group.thankview-ssh-access.id]
  enable_volume_tags     = true
  root_block_device = [
    {
      encrypted   = true
      volume_type = "gp3"
      throughput  = 125
      volume_size = 32
    },
  ]
  user_data_replace_on_change = false
  tags = {
    VantaProd        = "true"
    VantaDescription = "Jenkins server"
    VantaOwner       = "<EMAIL>"
  }
}

module "redis_host" {
  source                 = "terraform-aws-modules/ec2-instance/aws"
  version                = "~> 5.6"
  ami                    = data.aws_ami.ubuntu_20.id
  ignore_ami_changes     = true
  name                   = "prod-redis-host"
  instance_type          = "t3a.large"
  iam_instance_profile   = "thankview-ssm-ec2-instance-role"
  key_name               = "thankview-prod-ec2"
  subnet_id              = local.instance_subnet_id
  vpc_security_group_ids = [aws_security_group.webserver_redis_access.id, aws_security_group.thankview-ssh-access.id]
  enable_volume_tags     = false
  root_block_device = [
    {
      encrypted   = true
      volume_type = "gp3"
      throughput  = 125
      volume_size = 96
      tags = {
        Name = "prod-redis-host"
      }
    },
  ]
  user_data_replace_on_change = false
  tags = {
    VantaProd = "true"
  }
}

module "scheduler_host" {
  source                 = "terraform-aws-modules/ec2-instance/aws"
  version                = "~> 5.6"
  ami                    = data.aws_ami.ubuntu_20.id
  ignore_ami_changes     = true
  name                   = "prod-scheduler-host"
  instance_type          = "t3a.large"
  iam_instance_profile   = "thankview-ssm-ec2-instance-role"
  key_name               = "thankview-prod-ec2"
  subnet_id              = local.instance_subnet_id
  vpc_security_group_ids = [aws_security_group.webserver.id, aws_security_group.webserver_load_balancer.id, aws_security_group.thankview-ssh-access.id]
  enable_volume_tags     = false
  root_block_device = [
    {
      encrypted   = true
      volume_type = "gp3"
      throughput  = 125
      volume_size = 32
      tags = {
        Name        = "prod-scheduler-host"
        APP_ROLE    = "schedules"
        Team        = "devops"
        Environment = "prod"
        Service     = "infrastructure"
      }
    },
  ]
  user_data_replace_on_change = false
  tags = {
    VantaProd        = "true"
    VantaDescription = "Internal scheduler and reporting server"
    VantaOwner       = "<EMAIL>"
  }
}

module "sftp_host" {
  source                 = "terraform-aws-modules/ec2-instance/aws"
  version                = "~> 5.6"
  ami                    = data.aws_ami.ubuntu_20.id
  ignore_ami_changes     = true
  name                   = "prod-sftp-host"
  instance_type          = "t3a.large"
  iam_instance_profile   = "thankview-ssm-ec2-instance-role"
  key_name               = "thankview-prod-ec2"
  subnet_id              = local.instance_subnet_id
  vpc_security_group_ids = [aws_security_group.thankview-ssh-access.id, "sg-055c5d57c7cdd892b"]
  enable_volume_tags     = false
  root_block_device = [
    {
      encrypted   = true
      volume_type = "gp3"
      throughput  = 125
      volume_size = 192
      tags = {
        Name = "prod-sftp-host"
      }
    },
  ]
  user_data_replace_on_change = false
  tags = {
    VantaProd   = "true"
    APP_ROLE    = "sftp"
    Environment = "prod"
    Name        = "prod-sftp-host"
    Service     = "infrastructure"
    Team        = "devops"
  }
}



module "deployment-server" {
  source                 = "terraform-aws-modules/ec2-instance/aws"
  version                = "~> 5.6"
  ami                    = data.aws_ami.ubuntu_20.id
  ignore_ami_changes     = true
  name                   = "deployment-server"
  instance_type          = "t3a.small"
  iam_instance_profile   = "thankview-ssm-ec2-instance-role"
  key_name               = "thankview-prod-ec2"
  subnet_id              = local.instance_subnet_id
  vpc_security_group_ids = [aws_security_group.deployment_server_sg.id, aws_security_group.thankview-ssh-access.id]
  enable_volume_tags     = false
  root_block_device = [
    {
      encrypted   = true
      volume_type = "gp3"
      throughput  = 125
      volume_size = 20
      tags = {
        Name = "deployment-server"
      }
    },
  ]
  user_data_replace_on_change = false
  tags = {
    VantaProd = "true"
  }
}