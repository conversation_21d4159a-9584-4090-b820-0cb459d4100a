data "aws_ami" "ubuntu_18" {
  most_recent = true

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-bionic-18.04-amd64-server*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  owners = ["099720109477"] # Canonical's ID number
}

data "aws_iam_policy_document" "webserver_assume_role" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "webserver_secrets_access" {
  statement {
    actions = [
      "s3:GetObject",
      "s3:GetObjectVersion",
      "s3:ListBucket",
      "kms:Decrypt"
    ]
    resources = [
      "*"
    ]
  }
  statement {
    actions = [
      "ssm:GetParameter"
    ]
    resources = [
      "arn:aws:ssm:*:*:parameter/thankview-ps/prod/*"
    ]
  }
}

resource "aws_iam_role" "webserver" {
  name               = "webserver-prod"
  assume_role_policy = data.aws_iam_policy_document.webserver_assume_role.json
}

resource "aws_iam_role_policy" "webserver_secrets_access" {
  name   = "webserver-secrets-access"
  role   = aws_iam_role.webserver.id
  policy = data.aws_iam_policy_document.webserver_secrets_access.json
}

resource "aws_iam_instance_profile" "webserver" {
  name = "webserver-prod"
  role = aws_iam_role.webserver.name
}

locals {
  instance_count = 2
}

resource "aws_eip" "webserver_ip" {
  count = local.instance_count
}

resource "aws_instance" "webserver" {
  count         = local.instance_count
  instance_type = "t3a.xlarge"

  iam_instance_profile = aws_iam_instance_profile.webserver.name
  ami                  = data.aws_ami.ubuntu_18.id
  user_data = templatefile(
    "./config/webserver-cloudinit.yaml",
    {
      vhost_file_encoded   = filebase64("./config/apache.conf")
      fpm_php_ini_encoded  = filebase64("./config/fpm_php.ini")
      fpm_www_conf_encoded = filebase64("./config/fpm_www.conf")
      htaccess_encoded     = filebase64("./config/htaccess")
    }
  )

  ebs_optimized = true

  vpc_security_group_ids = [aws_security_group.webserver.id, "sg-0fc2847dcef594565"]

  tags = {
    Name      = "webserver-prod"
    VantaProd = "true"
  }

  lifecycle {
    ignore_changes = [
      user_data,
      ami
    ]
  }
}

resource "aws_eip_association" "webserver_ip_assoc" {
  count         = length(aws_instance.webserver.*.id)
  instance_id   = aws_instance.webserver[count.index].id
  allocation_id = aws_eip.webserver_ip[count.index].id
}
