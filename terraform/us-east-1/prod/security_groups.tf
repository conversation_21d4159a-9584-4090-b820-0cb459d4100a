resource "aws_security_group" "webserver" {
  name        = "webserver-prod"
  description = "Access needed for thankview webservers"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description     = "HTTP access from load balancer"
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = [aws_security_group.webserver_load_balancer.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "webserver_load_balancer" {
  name        = "webserver-lb-prod"
  description = "Access needed for thankview load balancer"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description = "Global HTTP"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description = "Global HTTPS"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "video-server" {
  name        = "video-server"
  description = "Access to and from video server"
  vpc_id      = data.aws_vpc.default.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "sends-server" {
  name        = "sends-server"
  description = "Access to and from sends server"
  vpc_id      = data.aws_vpc.default.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "thankview-ssh-access" {
  name        = "thankview-ssh-access"
  description = "Allows SSH access to developers"
  vpc_id      = data.aws_vpc.default.id


  ingress {
    description = "Haley IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Michelle H temp"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["***************/32"]
  }

   ingress {
    description = "Coronado IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  } 

  ingress {
    description = "Michael IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }
  
  ingress {
    description = "Yee IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Doug IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }
  ingress {
    description = "Jerry IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Joe IP address"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }

  ingress {
    description = "test icmp from shared services prod"
    from_port   = -1
    to_port     = -1
    protocol    = "icmp"
    cidr_blocks = ["*********/16"]
  }


  ingress {
    description = "deployment server public ip"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "deployment server"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

}


resource "aws_security_group" "webserver_mysql_access" {
  name        = "webserver-mysql-prod"
  description = "Access needed for thankview database"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description     = "Webserver mysql access"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.webserver.id]
  }

  ingress {
    description     = "Allow Video Server ASG instances"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.video-server.id]
  }

  ingress {
    description     = "Allow Send Server ASG instances"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.sends-server.id]
  }

  ingress {
    description     = "SFTP acesss"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = ["sg-055c5d57c7cdd892b"]
  }

  ingress {
    description = "Media 3 Server"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Media 5 Server"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Media 4 Server"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Secure Server"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32", "************/32"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "webserver_mysql_access_readonly" {
  name        = "webserver-mysql-prod-readonly"
  description = "Readonly access to mysql prod"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description     = "Bastion Host MySQL Access"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.bastion_host_sg.id]
  }

  ingress {
    description     = "DMS task"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = ["sg-0307c6344e7b8e48a"]
  }

  ingress {
    description = "Joe IP address"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }

  ingress {
    description = "doug youch ip address"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "jerry ip address"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "yee ip address"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Klipfolio address 1"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Klipfolio address 2"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Klipfolio address 3"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Klipfolio address 4"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Klipfolio address 5"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Klipfolio address 6"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Klipfolio address 7"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["***********/32"]
  }

  ingress {
    description = "Klipfolio address 8"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Klipfolio address 9"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Danny Moncada ip"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Joshua Riley IP Address"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }

  ingress {
    description = "Joshua Riley School IP address"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Michelle Huynh"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }

  ingress {
    description = "John Kelly"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["***************/32"]
  }

  ingress {
    description = "Haley Brandt-Erichsen"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
  }
}




resource "aws_security_group" "webserver_redis_access" {
  name        = "webserver-redis-prod"
  description = "Access needed for thankview database"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    description = "Media 3 Server"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Secure Server"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["*************/32", "************/32"]
  }

  ingress {
    description = "SFTP acesss"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["************/32"]
  }

  ingress {
    description = "Media 5 Server"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Media 4 Server"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description     = "Allow Send Server ASG instances"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.sends-server.id]
  }

  ingress {
    description     = "Allow Video Server ASG instances"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.video-server.id]
  }

  ingress {
    description     = "web servers"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.webserver.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "bastion_host_sg" {
  name        = "bastion-host-prod"
  description = "Access from bastion host instance"
  vpc_id      = data.aws_vpc.default.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}


resource "aws_security_group" "deployment_server_sg" {
  name        = "deployment_server_sg"
  description = "allows access from all resources in VPC"
  vpc_id      = data.aws_vpc.default.id

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["**********/16"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "deployment-server-sg"
  }

}