module "mail_sqs_queue" {
  source = "github.com/terraform-aws-modules/terraform-aws-sqs"
  name   = "thankview-mail-queue"
  # policy                      = var.policy
  sqs_managed_sse_enabled    = false
  fifo_queue                 = false
  visibility_timeout_seconds = 1800
}

module "video_sqs_queue" {
  source = "github.com/terraform-aws-modules/terraform-aws-sqs"
  name   = "thankview-video-queue"
  # policy                      = var.policy
  sqs_managed_sse_enabled    = false
  fifo_queue                 = false
  visibility_timeout_seconds = 3600
}

module "dlq_sqs_queue" {
  source                  = "github.com/terraform-aws-modules/terraform-aws-sqs"
  name                    = "thankview-dlq-queue"
  fifo_queue              = false
  sqs_managed_sse_enabled = false
}


module "small_mail_sqs_queue" {
  source                     = "github.com/terraform-aws-modules/terraform-aws-sqs"
  name                       = "thankview-small-campaigns-mail-queue"
  fifo_queue                 = false
  sqs_managed_sse_enabled    = false
  visibility_timeout_seconds = 1800

}

module "thankview-sms-queue" {
  source                     = "github.com/terraform-aws-modules/terraform-aws-sqs"
  name                       = "thankview-sms-queue"
  fifo_queue                 = false
  sqs_managed_sse_enabled    = false
  visibility_timeout_seconds = 1800

}


module "thankview-video-preview-queue" {
  source                     = "github.com/terraform-aws-modules/terraform-aws-sqs"
  name                       = "thankview-video-preview-queue"
  fifo_queue                 = false
  sqs_managed_sse_enabled    = false
  visibility_timeout_seconds = 1800

}
