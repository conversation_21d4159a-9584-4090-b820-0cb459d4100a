data "aws_ami" "video_server" {
  most_recent = true

  filter {
    name   = "name"
    values = ["thankview-video-worker-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  owners = ["019190539304"]
}

data "aws_iam_policy_document" "videoserver_sqs_access" {
  statement {
    actions = [
      "sqs:DeleteMessage",
      "sqs:ReceiveMessage",
      "sqs:SendMessage"
    ]
    resources = [
      module.video_sqs_queue.queue_arn,
      module.thankview-video-preview-queue.queue_arn

    ]
  }
}

resource "aws_iam_role" "videoserver" {
  name               = "video-worker-role-prod"
  assume_role_policy = data.aws_iam_policy_document.webserver_assume_role.json
  managed_policy_arns = [
    "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
    "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
  ]

  inline_policy {
    name   = "videoserver-sqs-access"
    policy = data.aws_iam_policy_document.videoserver_sqs_access.json
  }

  inline_policy {
    name   = "videoserver-secrets-access"
    policy = data.aws_iam_policy_document.webserver_secrets_access.json
  }
}

resource "aws_iam_instance_profile" "videoserver" {
  name = "videoserver-prod"
  role = aws_iam_role.videoserver.name
}

resource "aws_launch_template" "video_conf" {
  name_prefix            = "thankview-static-video-workers"
  image_id               = data.aws_ami.video_server.id
  instance_type          = "c6a.2xlarge"
  ebs_optimized          = true
  update_default_version = true


  iam_instance_profile {
    name = aws_iam_instance_profile.videoserver.name
  }
  vpc_security_group_ids = [aws_security_group.video-server.id, aws_security_group.thankview-ssh-access.id]

  user_data = base64encode(templatefile(
    "./config/video-worker-cloudinit.yaml",
    {
      digicert_ca_encoded                  = filebase64("./config/DigiCertSHA2SecureServerCA.crt.pem")
      elasticsearch_ca_encoded             = filebase64("./config/elasticsearch-ca.pem")
      video_worker_supervisor_conf_encoded = filebase64("./config/video-worker-supervisor.conf")
      datadog_api_key                      = data.aws_ssm_parameter.datadog_api_key.value #datadog SSM parameter value
    }
  ))

  block_device_mappings {
    device_name = "/dev/sda1"
    ebs {
      volume_size           = 32    # Match this to your AMI's volume size if you want to keep it
      delete_on_termination = true  # Set to true to ensure the volume is deleted upon instance termination
      volume_type           = "gp3" # Specify your desired volume type, matching AMI or as needed
      encrypted             = true  # Set according to your encryption needs
    }
  }

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name             = "video-worker-prod-static",
      VantaDescription = "Video worker - static",
      VantaOwner       = "<EMAIL>",
      VantaProd        = "true"
      Team             = "devops"
      Service          = "video-worker"
      Environment      = "prod"
      APP_ROLE         = "video-worker"

    }
  }

  tag_specifications {
    resource_type = "volume"
    tags = {
      Name = "video-worker-prod-static"
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_autoscaling_group" "video_static_autoscaling" {
  name             = "thankview-static-video-workers"
  min_size         = 6
  max_size         = 6
  desired_capacity = 6
  availability_zones = [
    "us-east-1a",
    "us-east-1b",
    "us-east-1c"
  ]
  enabled_metrics = [
    "GroupAndWarmPoolDesiredCapacity",
    "GroupAndWarmPoolTotalCapacity",
    "GroupDesiredCapacity",
    "GroupInServiceCapacity",
    "GroupInServiceInstances",
    "GroupMaxSize",
    "GroupMinSize",
    "GroupPendingCapacity",
    "GroupPendingInstances",
    "GroupStandbyCapacity",
    "GroupStandbyInstances",
    "GroupTerminatingCapacity",
    "GroupTerminatingInstances",
    "GroupTotalCapacity",
    "GroupTotalInstances",
    "WarmPoolDesiredCapacity",
    "WarmPoolMinSize",
    "WarmPoolPendingCapacity",
    "WarmPoolTerminatingCapacity",
    "WarmPoolTotalCapacity",
    "WarmPoolWarmedCapacity",
  ]

  launch_template {
    id      = aws_launch_template.video_conf.id
    version = "$Latest"
  }

  instance_refresh {
    strategy = "Rolling"
    preferences {
      instance_warmup = 120
    }
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes = [
      max_size,
      desired_capacity
    ]
  }
}

