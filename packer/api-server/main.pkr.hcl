packer {
  required_plugins {
    amazon = {
      source  = "github.com/hashicorp/amazon"
      version = "~> 1"
    }
  }
}

variable "version" {
  type = string
}

locals {
  timestamp = regex_replace(timestamp(), "[- TZ:]", "")
}

data "amazon-ami" "ubuntu" {
  filters = {
    name                = "ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-*"
    root-device-type    = "ebs"
    virtualization-type = "hvm"
  }
  most_recent = true
  owners      = ["099720109477"] # Canonical
  region      = "us-east-1"
}

source "amazon-ebs" "autogenerated_1" {
  ami_name             = "thankview-api-server-${var.version}"
  instance_type        = "t3a.medium"
  region               = "us-east-1"
  source_ami           = data.amazon-ami.ubuntu.id
  ssh_username         = "ubuntu"
  iam_instance_profile = "videoserver-prod"

  launch_block_device_mappings {
    device_name = "/dev/sda1"
    encrypted   = true
    volume_size = 32
    volume_type = "gp3"
  }
}

build {
  sources = ["source.amazon-ebs.autogenerated_1"]

  provisioner "file" {
    destination = "/tmp/nodesource.gpg.key"
    source      = "./config/nodesource.gpg.key"
  }

  # Upload SSH key for GitHub access
  # provisioner "file" {
  #   source      = "/Users/<USER>/Documents/Keys/packer_github_key"
  #   destination = "/tmp/packer_github_key"
  # }

  # Set up SSH and permissions
  # provisioner "shell" {
  #   inline = [
  #     "mkdir -p /home/<USER>/.ssh",
  #     "mv /tmp/packer_github_key /home/<USER>/.ssh/id_rsa",
  #     "chown ubuntu:ubuntu /home/<USER>/.ssh/id_rsa",
  #     "chmod 600 /home/<USER>/.ssh/id_rsa",
  #     "ssh-keyscan github.com >> /home/<USER>/.ssh/known_hosts",
  #     "chown ubuntu:ubuntu /home/<USER>/.ssh/known_hosts"
  #   ]
  # }

  # Run installs script
  provisioner "shell" {
    script = "./config/installs.sh"
  }
}
